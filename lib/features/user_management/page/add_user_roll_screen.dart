import 'dart:io';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:flowkar/features/user_management/model/user_role_request_model.dart';

class AddUserListScreen extends StatefulWidget {
  const AddUserListScreen({super.key});
  static Widget builder(BuildContext context) {
    return AddUserListScreen();
  }

  @override
  State<AddUserListScreen> createState() => _AddUserListScreenState();
}

class _AddUserListScreenState extends State<AddUserListScreen> {
  final TextEditingController _roleNameController = TextEditingController();

  final ValueNotifier<bool> isPostUpload = ValueNotifier(false);
  final ValueNotifier<bool> isMessage = ValueNotifier(false);
  final ValueNotifier<bool> isAnalytics = ValueNotifier(false);
  final ValueNotifier<bool> isUserManagement = ValueNotifier(false);
  // final ValueNotifier<bool> isBrandManagement = ValueNotifier(false);
  final ValueNotifier<bool> isBlockUnblock = ValueNotifier(false);
  final ValueNotifier<bool> isFeedback = ValueNotifier(false);
  final ValueNotifier<bool> isPostUploadWithPermission = ValueNotifier(false);

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();


  @override
  Widget build(BuildContext context) {
      bool get isAnyPermissionSelected =>
      isPostUpload.value ||
      isMessage.value ||
      isAnalytics.value ||
      isUserManagement.value ||
      isBlockUnblock.value ||
      isFeedback.value ||
      isPostUploadWithPermission.value;
    return Scaffold(
      appBar: _buildSearchUserManageAppBar(context),
      body: Padding(
        padding: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
        child: BlocBuilder<UserManagementBloc, UserManagementState>(
          builder: (context, state) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Form(
                key: formKey,
                child: InkWell(
                  focusColor: Colors.transparent,
                  onTap: () {
                    FocusManager.instance.primaryFocus?.unfocus();
                  },
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              buildSizedBoxH(8.0),
                              _buildRollName(context),
                              buildSizedBoxH(18.0),
                              _buildAddRollPermition()
                            ],
                          ),
                        ),
                      ),
                      buildSizedBoxH(16.0),
                      _buildAddRoleAndCancelBotton(state),
                      buildSizedBoxH(30.0)
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildSearchUserManageAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.current.lbl_add_role,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildRollName(BuildContext ctx) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _roleNameController,
      builder: (context, value, child) {
        return FlowkarTextFormField(
          labelText: Lang.current.lbl_role_name,
          context: ctx,
          controller: _roleNameController,
          validator: AppValidations.validateRoleName,
        );
      },
    );
  }

  Widget _buildAddRollPermition() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.current.lbl_add_permissions,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.sp, fontWeight: FontWeight.w600),
        ),
        buildSizedBoxH(10.0),
        Wrap(
          spacing: 12.w,
          children: [
            ValueListenableBuilder<bool>(
              valueListenable: isPostUpload,
              builder: (context, value, child) {
                return _buildPermissionChip(
                  Lang.current.lbl_post_upload,
                  value,
                  (val) {
                    if (!isPostUpload.value) {
                      isPostUpload.value = val;
                    } else {
                      isPostUpload.value = false;
                    }
                  },
                  Color(0xffF1F1F1),
                );
              },
            ),
            ValueListenableBuilder<bool>(
              valueListenable: isMessage,
              builder: (context, value, child) {
                return _buildPermissionChip(
                  Lang.current.lbl_message,
                  value,
                  (val) {
                    if (!isMessage.value) {
                      isMessage.value = val;
                    } else {
                      isMessage.value = false;
                    }
                  },
                  Color(0xffF1F1F1),
                );
              },
            ),
            ValueListenableBuilder<bool>(
              valueListenable: isPostUploadWithPermission,
              builder: (context, value, child) {
                return _buildPermissionChip(
                  Lang.current.lbl_post_upload_with_permission,
                  value,
                  (val) {
                    if (!isPostUploadWithPermission.value) {
                      isPostUploadWithPermission.value = val;
                    } else {
                      isPostUploadWithPermission.value = false;
                    }
                  },
                  Color(0xffF1F1F1),
                );
              },
            ),
            ValueListenableBuilder<bool>(
              valueListenable: isAnalytics,
              builder: (context, value, child) {
                return _buildPermissionChip(
                  Lang.current.lbl_analytics,
                  value,
                  (val) {
                    if (!isAnalytics.value) {
                      isAnalytics.value = val;
                    } else {
                      isAnalytics.value = false;
                    }
                  },
                  Color(0xffF1F1F1),
                );
              },
            ),
            ValueListenableBuilder<bool>(
              valueListenable: isUserManagement,
              builder: (context, value, child) {
                return _buildPermissionChip(
                  Lang.current.lbl_user_management,
                  value,
                  (val) {
                    if (!isUserManagement.value) {
                      isUserManagement.value = val;
                    } else {
                      isUserManagement.value = false;
                    }
                  },
                  Color(0xffF1F1F1),
                );
              },
            ),
            // ValueListenableBuilder<bool>(
            //   valueListenable: isBrandManagement,
            //   builder: (context, value, child) {
            //     return _buildPermissionChip(
            //       Lang.current.lbl_brand_management,
            //       value,
            //       (val) {
            //         if (!isBrandManagement.value) {
            //           isBrandManagement.value = val;
            //         } else {
            //           isBrandManagement.value = false;
            //         }
            //       },
            //       Color(0xffF1F1F1),
            //     );
            //   },
            // ),
            ValueListenableBuilder<bool>(
              valueListenable: isBlockUnblock,
              builder: (context, value, child) {
                return _buildPermissionChip(
                  Lang.current.lbl_block_unblock,
                  value,
                  (val) {
                    if (!isBlockUnblock.value) {
                      isBlockUnblock.value = val;
                    } else {
                      isBlockUnblock.value = false;
                    }
                  },
                  Color(0xffF1F1F1),
                );
              },
            ),
            ValueListenableBuilder<bool>(
              valueListenable: isFeedback,
              builder: (context, value, child) {
                return _buildPermissionChip(
                  Lang.current.lbl_feedback,
                  value,
                  (val) {
                    if (!isFeedback.value) {
                      isFeedback.value = val;
                    } else {
                      isFeedback.value = false;
                    }
                  },
                  Color(0xffF1F1F1),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPermissionChip(
    String label,
    bool isSelected,
    Function(bool) onSelected,
    Color backgroundColor,
  ) {
    return FilterChip(
      chipAnimationStyle: ChipAnimationStyle(selectAnimation: AnimationStyle(duration: Duration(milliseconds: 300))),
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 14.sp,
                  color: isSelected ? Theme.of(context).customColors.white : Theme.of(context).primaryColor,
                ),
          ),
        ],
      ),
      selected: isSelected,
      onSelected: onSelected,
      backgroundColor: backgroundColor,
      selectedColor: Theme.of(context).primaryColor,
      checkmarkColor: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      shape: RoundedRectangleBorder(
        side: BorderSide(
          color: Color(0xffF1F1F1),
        ),
        borderRadius: BorderRadius.circular(20.r),
      ),
    );
  }

  Widget _buildAddRoleAndCancelBotton(UserManagementState state) {
    return ValueListenableBuilder(
      valueListenable: isPostUpload, // Can be any of the permission notifiers
      builder: (context, _, __) {
        return Row(
          children: [
            Expanded(
              child: CustomElevatedButton(
                text: Lang.of(context).lbl_create,
                isDisabled: !isAnyPermissionSelected || state.getUserRolesLoading,
                isLoading: state.getUserRolesLoading,
                onPressed: !isAnyPermissionSelected || state.getUserRolesLoading
                    ? null
                    : () {
                        if (formKey.currentState!.validate()) {
                          final roleData = UserRoleRequestModel(
                            roleName: _roleNameController.text,
                            roleDescription: {
                              if (isPostUpload.value) "1": "Post Upload",
                              if (isMessage.value) "2": "Message",
                              if (isAnalytics.value) "3": "Analytics",
                              if (isUserManagement.value) "4": "User Management",
                              if (isBlockUnblock.value) "6": "Block & Unblock",
                              if (isFeedback.value) "7": "Feedback",
                              if (isPostUploadWithPermission.value) "8": "Post Upload With Permission",
                            },
                          );
                          context.read<UserManagementBloc>().add(CreateUserRoleEvent(roleData));
                        }
                      },
              ),
            ),
          ],
        );
      },
    );
  }
}
