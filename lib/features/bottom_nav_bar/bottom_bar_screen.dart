// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'dart:async';
import 'dart:io';

import 'package:flowkar/core/deep_link_route/deep_link_route.dart';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/bloc/analytics_bloc.dart';
import 'package:flowkar/features/analytics/presentation/page/platform_analytics.dart';
import 'package:flowkar/features/discover/page/discover_page.dart';
import 'package:flowkar/features/live_stream/presentation/live_stream.dart';
import 'package:flowkar/features/notification/one_signal_notification/one_signal_config.dart';
import 'package:flowkar/features/notification/page/notification_screen.dart';
import 'package:flowkar/features/reels_screen/presentation/pages/video_page.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_bloc.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_event.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';
import 'package:flowkar/features/sm_chat/presentation/sm_chat_screen.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/survey_form/presentation/page/survey_form_page.dart';
import 'package:flowkar/features/update_version/bloc/version_bloc.dart';
import 'package:flowkar/features/update_version/helper/in_app_update_helper.dart';
import 'package:flowkar/features/upload_post/presentation/widget/image_picker.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:intl/intl.dart';
import 'package:permission_asker/permission_asker.dart';
import 'package:quick_actions/quick_actions.dart';
import 'package:story_editor/story_editor.dart';

class BottomNavBar extends StatefulWidget {
  const BottomNavBar({super.key});

  static Widget builder(BuildContext context) {
    return BottomNavBar();
  }

  @override
  State<BottomNavBar> createState() => _BottomNavBarState();
}

class _BottomNavBarState extends State<BottomNavBar> with WidgetsBindingObserver {
  // GlobalKey<NavigatorState> homeNavigatorKey = GlobalKey<NavigatorState>();
  QuickActions quickActions = QuickActions();

  late PersistentTabController _controller;
  bool hideBottomBar = false;
  bool initial = true;

  bool _isApiCalled = false;
  // late bool isPostPermission;
  // late bool isMassagePermission;
  // late bool isanalyticsPermission;

  @override
  initState() {
    // isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    // isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    // isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    DeepLinkRoute().initUniLinks(context);

    super.initState();
    _checkAndRequestNotificationPermission();
    _controller = PersistentTabController(initialIndex: 0)
      ..addListener(() {
        setState(() {});
        // isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
        // isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
        // isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
      });
    initializeQuickActions();
    WidgetsBinding.instance.addObserver(this);

    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
    _callLeaderboardAPIOncePerDay();
    _showWelcomeDialog();
    _joinsocket();
  }

  initializeQuickActions() {
    quickActions.initialize((String shortcutType) async {
      switch (shortcutType) {
        case 'Upload':
          context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
          final status = await Permission.storage.request();
          final camera = await Permission.camera.request();
          final microphone = await Permission.microphone.request();
          final manageExternalStorage = await Permission.manageExternalStorage.request();
          if (status.isGranted && camera.isGranted && microphone.isGranted && manageExternalStorage.isGranted) {
            Logger.lOG('Storage permission granted');
          } else {
            // openAppSettings();
            Logger.lOG('Storage permission denied');
          }
          PickMediaWidget().pickImages(context, 1, 'New Post');
          Logger.lOG('Add Post tapped');
          return;
        case 'Chat':
          _controller.jumpToTab(2);
          return;
        case 'Activity':
          context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: NotificationPage(),
            withNavBar: false,
            pageTransitionAnimation: PageTransitionAnimation.cupertino,
          );
          return;
        default:
          NavigatorService.pushNamedAndRemoveUntil(AppRoutes.bottomNavBar);
          return;
      }
    });

    quickActions.setShortcutItems(
      <ShortcutItem>[
        ShortcutItem(
          type: 'Upload Content',
          localizedTitle: 'Upload Content',
          icon: "ic_upload",
        ),
        ShortcutItem(
          type: 'Messages',
          localizedTitle: 'Messages',
          icon: "ic_chat",
        ),
        ShortcutItem(
          type: 'Recent Activities',
          localizedTitle: 'Recent Activities',
          icon: "ic_activity",
        ),
      ],
    );
  }

  Future<void> _callLeaderboardAPIOncePerDay() async {
    String todayDate = DateFormat("dd MMM, yyyy").format(DateTime.now());
    final String? lastCallDate = Prefobj.preferences?.get(Prefkeys.LEADERBOARD_LAST_CALL_DATE);

    if (lastCallDate != todayDate) {
      context.read<LeaderboardBloc>().add(GetRewardDataEvent(context: context));
      await Prefobj.preferences?.put(Prefkeys.LEADERBOARD_LAST_CALL_DATE, todayDate);
      Logger.lOG('LeaderboardBloc API called for date: $todayDate');
    } else {
      Logger.lOG('LeaderboardBloc API already called today: $todayDate');
    }
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    // isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    // isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    // isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    setState(() {});
    if (state == AppLifecycleState.paused) {
      // App is in the background
      SocketService.closeConnection();
    } else if (state == AppLifecycleState.detached) {
      // App is completely closed
    } else if (state == AppLifecycleState.resumed) {
      // initializeQuickActions();
      _showWelcomeDialog();
      if (!_isApiCalled) {
        _loadApi();
      }
      _checkPermissionAfterReturning();
      SocketService.initializeSocket();
      _callLeaderboardAPIOncePerDay();

      Future.delayed(const Duration(seconds: 2), () {
        Logger.lOG("RECONNECT");
        if (Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) != null) {
          SocketService.emit(APIConfig.joinSocket, {
            'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
          });
          SocketService.response(
            APIConfig.joinSocket,
            (joinSocket) {},
          );
        }
      });

      // DeepLinkRoute().reinit(context);
    }
  }

  @override
  void dispose() {
    DeepLinkRoute().dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _checkPermissionAfterReturning() async {
    // isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    // isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    // isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    if (await Permission.notification.status.isGranted) {
      scheduleMicrotask(() async => await initializeOneSignalNotification());
    } else {
      setState(() {});
    }
  }

  Future<void> _checkAndRequestNotificationPermission() async {
    var status = await Permission.notification.status;
    if (!_isApiCalled) {
      _loadApi();
      // _isApiCalled = true;
    }
    if (status.isGranted) {
    } else if (status.isDenied || status.isPermanentlyDenied) {
      setState(() {});
    }
  }

  _loadApi() async {
    if (_isApiCalled) return;
    _isApiCalled = true;
    setState(() {
      Logger.lOG('Version Update Dialog');
    });
    Logger.lOG('Version Dialog');
    await _checkForAppUpdate();
  }

  void _showWelcomeDialog() {
    // var state = context.read<SurveyBloc>().state;
    if (Prefobj.preferences?.get(Prefkeys.STATUS).toString() == '4') {
      Future.delayed(Duration(milliseconds: 500), () {
        isDialogShowing == true ? SizedBox.shrink() : SurveyDialog.show(context);
      });
    }
  }

  void _joinsocket() {
    SocketService.initializeSocket();
    Future.delayed(const Duration(seconds: 2), () {
      // isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
      // isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
      // isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
      Logger.lOG("RECONNECT");
      if (Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) != null) {
        SocketService.emit(APIConfig.joinSocket, {
          'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        });
        SocketService.response(
          APIConfig.joinSocket,
          (joinSocket) {},
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    // isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    // isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark.copyWith(
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Theme.of(context).primaryColor,
          systemNavigationBarIconBrightness: Brightness.dark),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        extendBody: true,
        body: Container(
          margin: EdgeInsets.only(bottom: 0),
          // margin: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(100.r), boxShadow: [
            BoxShadow(color: Colors.black, blurRadius: 10),
          ]),
          child: PersistentTabView(
            floatingActionButton: Padding(
              padding: EdgeInsets.only(bottom: Platform.isIOS ? 16.h : 10.h, right: 5.w),
              child: ValueListenableBuilder<bool?>(
                  valueListenable: isPostPermissionNotifier,
                  builder: (context, hasPermission, _) {
                    return SpeedDial(
                      onOpen: hasPermission ?? false
                          ? null
                          : () {
                              showToastNoPermission(access: '');
                            },
                      animatedIconTheme: IconThemeData(size: 32.sp),
                      backgroundColor: hasPermission ?? false ? Theme.of(context).primaryColor : Color(0xffA39795),
                      overlayOpacity: 0.0,
                      children: hasPermission ?? false
                          ? [
                              SpeedDialChild(
                                child: CustomImageView(imagePath: Assets.images.svg.homeFeed.svgAddPost.path),
                                label: 'Add Story',
                                labelStyle: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(fontSize: 14.sp, fontWeight: FontWeight.w700),
                                onTap: () async {
                                  final status = await Permission.storage.request();
                                  final camera = await Permission.camera.request();
                                  final microphone = await Permission.microphone.request();
                                  final manageExternalStorage = await Permission.manageExternalStorage.request();
                                  bool isFacebook = await Prefobj.preferences?.get(Prefkeys.FACEBOOK);
                                  bool isInstagram = await Prefobj.preferences?.get(Prefkeys.INSTAGRAM);
                                  if (status.isGranted &&
                                      camera.isGranted &&
                                      microphone.isGranted &&
                                      manageExternalStorage.isGranted) {
                                    Logger.lOG('Storage permission granted');
                                  } else {
                                    Logger.lOG('Storage permission denied');
                                  }
                                  Navigator.push(
                                    //  use_build_context_synchronously
                                    context,
                                    PageRouteBuilder(
                                      pageBuilder: (context, animation, secondaryAnimation) => flowkarStoryEditor(
                                        centerText: "Start Your Design",
                                        isfacebook: isFacebook,
                                        isInstagram: isInstagram,
                                        onDone: (media, instagram, facebook) {
                                          context.read<HomeFeedBloc>().add(
                                                UploadStoryApiEvent(
                                                  uploadFiles: File(media),
                                                  music: '',
                                                  title: '',
                                                  isFacebook: facebook,
                                                  isInstagram: instagram,
                                                ),
                                              );
                                          // Navigator.pop(context);
                                        },
                                        onDoneHighlight: (media, title) {
                                          Logger.lOG(title);
                                          context.read<HomeFeedBloc>().add(
                                                UploadStoryApiEvent(
                                                  uploadFiles: File(media),
                                                  music: '',
                                                  title: title,
                                                  isFacebook: false,
                                                  isInstagram: false,
                                                ),
                                              );
                                          // Navigator.pop(context);
                                        },
                                      ),
                                    ),
                                  );
                                  Logger.lOG('Add Story tapped');
                                },
                              ),
                              SpeedDialChild(
                                  child: CustomImageView(imagePath: Assets.images.svg.homeFeed.svgStory.path),
                                  label: 'Create Post',
                                  labelStyle: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(fontSize: 14.sp, fontWeight: FontWeight.w700),
                                  onTap: () async {
                                    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                                    final status = await Permission.storage.request();
                                    final camera = await Permission.camera.request();
                                    final microphone = await Permission.microphone.request();
                                    final manageExternalStorage = await Permission.manageExternalStorage.request();
                                    if (status.isGranted &&
                                        camera.isGranted &&
                                        microphone.isGranted &&
                                        manageExternalStorage.isGranted) {
                                      Logger.lOG('Storage permission granted');
                                    } else {
                                      // openAppSettings();
                                      Logger.lOG('Storage permission denied');
                                    }
                                    PickMediaWidget().pickImages(context, 1, 'New Post');
                                    Logger.lOG('Add Post tapped');
                                  }),
                              SpeedDialChild(
                                  child: CustomImageView(imagePath: Assets.images.svg.homeFeed.svgTextPost.path),
                                  label: 'Create Text Post',
                                  labelStyle: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(fontSize: 14.sp, fontWeight: FontWeight.w700),
                                  onTap: () async {
                                    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                                    // final status = await Permission.storage.request();
                                    // final camera = await Permission.camera.request();
                                    // final microphone = await Permission.microphone.request();
                                    // final manageExternalStorage = await Permission.manageExternalStorage.request();
                                    // if (status.isGranted &&
                                    //     camera.isGranted &&
                                    //     microphone.isGranted &&
                                    //     manageExternalStorage.isGranted) {
                                    //   Logger.lOG('Storage permission granted');
                                    // } else {
                                    //   // openAppSettings();
                                    //   Logger.lOG('Storage permission denied');
                                    // }
                                    // NavigatorService.pushNamed(AppRoutes.uploadPostScrteen, arguments: [true]);
                                    NavigatorService.pushNamed(AppRoutes.uploadPostScrteen,
                                        arguments: [[], true, false, ""]);
                                    Logger.lOG('Add Text Post tapped');
                                  }),
                              SpeedDialChild(
                                  child: CustomImageView(imagePath: Assets.images.svg.homeFeed.svgLive.path),
                                  label: 'Go Live',
                                  labelStyle: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(fontSize: 14.sp, fontWeight: FontWeight.w700),
                                  onTap: () async {
                                    final status = await Permission.camera.request();
                                    final microphone = await Permission.microphone.request();
                                    if (status.isGranted && microphone.isGranted) {
                                      // NavigatorService.pushNamed(AppRoutes.livestreamScreen, arguments: [
                                      //   'liv-${Prefobj.preferences?.get(Prefkeys.USER_ID)}',
                                      //   Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                      // ]);
                                      PersistentNavBarNavigator.pushNewScreen(
                                        context,
                                        screen: LiveStreamPage(
                                          args: [
                                            'liv-${Prefobj.preferences?.get(Prefkeys.USER_ID)}',
                                            Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                          ],
                                        ),
                                        withNavBar: false,
                                        customPageRoute: PageRouteBuilder(
                                          opaque: false,
                                          barrierColor: Colors.transparent,
                                          transitionDuration: Duration(milliseconds: 250),
                                          reverseTransitionDuration: Duration(milliseconds: 250),
                                          pageBuilder: (context, animation, secondaryAnimation) {
                                            return LiveStreamPage(
                                              args: [
                                                'liv-${Prefobj.preferences?.get(Prefkeys.USER_ID)}',
                                                Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                              ],
                                            );
                                          },
                                          transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                            final curvedAnimation = CurvedAnimation(
                                              parent: animation,
                                              curve: Curves.easeInOut,
                                            );

                                            return SlideTransition(
                                              position: Tween<Offset>(
                                                begin: Offset(0, 1),
                                                end: Offset.zero,
                                              ).animate(curvedAnimation),
                                              child: SlideTransition(
                                                position: Tween<Offset>(
                                                  begin: Offset.zero,
                                                  end: Offset(0, 1),
                                                ).animate(CurvedAnimation(
                                                  parent: secondaryAnimation,
                                                  curve: Curves.easeInOut,
                                                )),
                                                child: child,
                                              ),
                                            );
                                          },
                                        ),
                                      );
                                    }
                                    // NavigatorService.pushNamed(AppRoutes.livestreamScreen, arguments: [
                                    //   'liv-${Prefobj.preferences?.get(Prefkeys.USER_ID)}',
                                    //   Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                    // ]);
                                  }),
                            ]
                          : [],
                      child: Icon(Icons.add_rounded,
                          size: 26.sp, color: hasPermission ?? false ? Colors.white : Colors.white70),
                    );
                  }),
            ),
            decoration: NavBarDecoration(
                borderRadius: BorderRadius.circular(100.r),
                useBackdropFilter: false,
                boxShadow: [BoxShadow(color: Colors.black26.withOpacity(0.2), blurRadius: 20)]),
            context,
            controller: _controller,
            bottomScreenMargin: 0.01,
            margin: EdgeInsets.only(left: 16.w, right: 80.w, bottom: Platform.isIOS ? 16.h : 10.h),
            screens: [
              // Navigator(
              //   key: homeNavigatorKey,
              //   onGenerateRoute: (routeSettings) {
              //     return MaterialPageRoute(
              //       builder: (context) => HomeFeedScreen(),
              //     );
              //   },
              // ),
              HomeFeedScreen(),
              DiscoverPage(),
              // ChatListScreen(),
              SmChatScreen(),
              SizedBox(),
              PlatformAnalytics(),
            ],
            onWillPop: (p0) async {
              if (_controller.index == 0) {
                final shouldPop = await showDialog<bool>(
                  context: context,
                  builder: (context) => CustomAlertDialog(
                    title: "Are you sure you want to exit the app?",
                    subtitle: '',
                    isLoading: false,
                    onConfirmButtonPressed: () {
                      Navigator.pop(context, true);
                      exit(0);
                    },
                    confirmButtonText: "Exit",
                  ),
                );

                if (shouldPop == true) {
                  SystemNavigator.pop();
                  return true;
                }

                return false;
              }

              return true;
            },
            items: _buildNavBarItems(),
            confineToSafeArea: true,
            backgroundColor: Colors.white,
            navBarHeight: kBottomNavigationBarHeight,
            handleAndroidBackButtonPress: true,
            hideNavigationBarWhenKeyboardAppears: true,
            stateManagement: true,
            navBarStyle: NavBarStyle.simple,
            animationSettings: const NavBarAnimationSettings(),
            popBehaviorOnSelectedNavBarItemPress: PopBehavior.all,
            selectedTabScreenContext: (p0) => context,
            resizeToAvoidBottomInset: false,
            onItemSelected: (index) {
              setState(() {
                _controller.index = index;
              });
            },
          ),
        ),
      ),
    );
  }

  List<PersistentBottomNavBarItem> _buildNavBarItems() {
    return [
      // Home Screen
      PersistentBottomNavBarItem(
          onSelectedTabPressWhenNoScreensPushed: () {
            Navigator.of(context).popUntil((route) => route.isFirst);
          },
          icon: InkWell(
              onTap: () {
                _controller.index = 0;
                context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
              },
              onDoubleTap: () {
                setState(() {
                  scrollTopNotifier.value = true;
                  context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                });
              },
              child: _buildNavBarIcon(
                  unselectedIcon: Assets.images.icons.bottomBar.icHomeUnselect.path,
                  selectedIcon: Assets.images.icons.bottomBar.icHomeSelect.path,
                  index: 0,
                  permision: true)),
          scrollToTopOnNavBarItemPress: true),

      // Search Screen
      PersistentBottomNavBarItem(
          icon: InkWell(
              onTap: () {
                _controller.index = 1;
                context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                FocusScope.of(context).unfocus();
              },
              child: _buildNavBarIcon(
                  unselectedIcon: Assets.images.icons.bottomBar.icSearchUnselect.path,
                  selectedIcon: Assets.images.icons.bottomBar.icSearchSelect.path,
                  index: 1,
                  permision: true)),
          scrollToTopOnNavBarItemPress: true),

      // Chat Screen
      PersistentBottomNavBarItem(
        icon: ValueListenableBuilder<bool?>(
          valueListenable: isMassagePermissionNotifier,
          builder: (context, hasPermission, child) {
            return InkWell(
              onTap: hasPermission ?? false
                  ? () {
                      if (_controller.index != 2) {
                        context.read<SurveyBloc>().add(
                              UserHomeDataAPI(context: context),
                            );
                        context.read<HomeFeedBloc>().add(
                              GetProfilePostEvent(),
                            );
                        context.read<SmChatBloc>().add(
                              const GetChatListEvent(page: 1, isReload: false),
                            );
                        context.read<SmChatBloc>().add(
                              GetSmchatListEvent(),
                            );
                        context.read<SmChatBloc>().add(
                              GetTelegramUerListAPI(),
                            );
                      }
                      _controller.index = 2;
                      FocusScope.of(context).unfocus();
                    }
                  : () {},
              child: _buildNavBarIcon(
                unselectedIcon: Assets.images.icons.bottomBar.icChatUnselect.path,
                selectedIcon: Assets.images.icons.bottomBar.icChatSelect.path,
                index: 2,
                permision: hasPermission ?? false,
              ),
            );
          },
        ),
        scrollToTopOnNavBarItemPress: true,
      ),

      // Reels Screen
      PersistentBottomNavBarItem(
          icon: InkWell(
              onTap: () {
                // _controller.index = 3;
                PersistentNavBarNavigator.pushNewScreen(context,
                    screen: VideoReelPage(
                      index: 0,
                      reelService: ReelService(),
                      screen: 'Bottom Nav',
                    ));
              },
              child: _buildNavBarIcon(
                  unselectedIcon: Assets.images.icons.bottomBar.icReelsUnselect.path,
                  selectedIcon: Assets.images.icons.bottomBar.icReelsSelect.path,
                  index: 3,
                  permision: true)),
          scrollToTopOnNavBarItemPress: true),

      // // Analytics Screen
      PersistentBottomNavBarItem(
        icon: ValueListenableBuilder<bool?>(
          valueListenable: isanalyticsPermissionNotifier,
          builder: (context, hasPermission, child) {
            return InkWell(
              onTap: hasPermission ?? false
                  ? () {
                      _controller.index = 4;
                      context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                      context.read<AnalyticsBloc>().add(GetAnalyticsSocialEvent());
                    }
                  : () {},
              child: _buildNavBarIcon(
                unselectedIcon: Assets.images.icons.bottomBar.icAnalyticsUnselect.path,
                selectedIcon: Assets.images.icons.bottomBar.icAnalyticsSelect.path,
                index: 4,
                permision: hasPermission ?? false,
              ),
            );
          },
        ),
      )
    ];
  }

// Custom Navigation Bar Icon Builder
  Widget _buildNavBarIcon(
      {required String unselectedIcon, required String selectedIcon, required int index, required bool permision}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        buildSizedBoxH(8),
        CustomImageView(
          imagePath: _controller.index == index ? selectedIcon : unselectedIcon,
          color: !permision ? Theme.of(context).customColors.greylite : null,
        ),
        buildSizedBoxH(4),
        Container(
          height: 7.h,
          width: 7.w,
          decoration: BoxDecoration(
              color: _controller.index == index ? Theme.of(context).primaryColor.withOpacity(0.3) : Colors.transparent,
              shape: BoxShape.circle),
        )
      ],
    );
  }

  _checkForAppUpdate() async {
    // settingsBloc.add(const VersionUpdateEvent());

    // await Future.delayed(const Duration(seconds: 2));
    // setState(() {});
    // final settingsBloc = context.read<VersionBloc>().state;

    // final versionModel = settingsBloc.versionModel;
    // if (versionModel != null) {
    //   await FlowkarAppUpdateHelper.checkForUpdate(context, versionModel);
    // }

    final settingsBloc = context.read<VersionBloc>();
    settingsBloc.add(const VersionUpdateEvent());

    await Future.delayed(const Duration(milliseconds: 1600));

    final versionModel = settingsBloc.state.versionModel;
    if (versionModel != null) {
      await FlowkarAppUpdateHelper.checkForUpdate(context, versionModel);
    }
  }
}
